{"name": "hire-helmsman", "private": true, "workspaces": ["apps/*", "services/*", "infra/*"], "scripts": {"prepare": "husky", "build": "npm run --workspaces --if-present build", "lint": "npm run --workspaces --if-present lint", "format": "prettier --write .", "typecheck": "npm run --workspaces --if-present typecheck", "test": "npm run --workspaces --if-present test"}, "devDependencies": {"@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "husky": "^9.1.7", "lint-staged": "^15.2.10", "prettier": "^3.3.3"}, "lint-staged": {"*.{json,css,md}": "prettier --write", "*.{ts,tsx,js,jsx}": "prettier --write"}}