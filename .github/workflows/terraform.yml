name: Infrastructure CI

on:
  pull_request:
    paths:
      - "infra/terraform/**"
      - ".github/workflows/terraform.yml"
  push:
    branches: [main]
    paths:
      - "infra/terraform/**"
      - ".github/workflows/terraform.yml"

# Avoid duplicate runs on the same ref
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  terraform:
    name: Terraform Validation
    runs-on: ubuntu-latest
    env:
      TF_IN_AUTOMATION: true
      TF_INPUT: 0

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.9.7

      - name: Terraform format check
        run: terraform fmt -check -recursive

      - name: Validate dev environment
        working-directory: infra/terraform/envs/dev
        run: |
          terraform init -backend=false -input=false
          terraform validate -no-color

      - name: Install tflint
        run: |
          curl -s https://raw.githubusercontent.com/terraform-linters/tflint/master/install_linux.sh | bash
          echo "$HOME/.tflint/bin" >> $GITHUB_PATH

      - name: Run tflint on root
        run: |
          tflint --init
          tflint -f compact

      - name: Run tflint on modules
        shell: bash
        run: |
          set -e
          for dir in infra/terraform/modules/*; do
            [ -d "$dir" ] || continue
            echo "===> tflint $dir"
            (cd "$dir" && tflint -f compact || true)
          done
