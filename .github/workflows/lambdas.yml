name: Lambda Services CI

on:
  pull_request:
    paths:
      - "services/lambdas/**"
      - ".github/workflows/lambdas.yml"
  push:
    branches: [main]
    paths:
      - "services/lambdas/**"
      - ".github/workflows/lambdas.yml"

# Avoid duplicate runs on the same ref
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  lambdas:
    name: Lambda Services (Python)
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"
          cache: "pip"
          cache-dependency-path: services/lambdas/*/requirements.txt

      - name: Install tooling
        run: |
          python -m pip install --upgrade pip
          pip install ruff mypy pytest

      - name: Install lambda dependencies
        shell: bash
        run: |
          set -e
          shopt -s nullglob
          files=(services/lambdas/*/requirements.txt)
          if [ ${#files[@]} -gt 0 ]; then
            for req in "${files[@]}"; do
              echo "Installing: $req"
              pip install -r "$req"
            done
          else
            echo "No lambda requirements.txt found"
          fi

      - name: Lint with Ruff
        run: ruff check services/lambdas

      - name: Type check with MyPy
        continue-on-error: true
        run: mypy services/lambdas

      - name: Run tests
        shell: bash
        run: |
          if ls services/lambdas/**/tests -d 1>/dev/null 2>&1; then
            pytest -q services/lambdas
          else
            echo "No Python tests found"
          fi
