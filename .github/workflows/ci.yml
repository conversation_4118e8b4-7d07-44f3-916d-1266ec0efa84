name: CI Pipeline

on:
  pull_request:
  push:
    branches: [main]

# Avoid duplicate runs on the same ref
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  # This job determines which components have changed
  changes:
    name: Detect Changes
    runs-on: ubuntu-latest
    outputs:
      web: ${{ steps.changes.outputs.web }}
      lambdas: ${{ steps.changes.outputs.lambdas }}
      terraform: ${{ steps.changes.outputs.terraform }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Check for changes
        uses: dorny/paths-filter@v3
        id: changes
        with:
          filters: |
            web:
              - 'apps/web/**'
              - 'package.json'
              - 'package-lock.json'
            lambdas:
              - 'services/lambdas/**'
            terraform:
              - 'infra/terraform/**'

  # Call individual workflow jobs based on changes
  web:
    name: Web App CI
    needs: changes
    if: ${{ needs.changes.outputs.web == 'true' }}
    uses: ./.github/workflows/web.yml

  lambdas:
    name: Lambda Services CI
    needs: changes
    if: ${{ needs.changes.outputs.lambdas == 'true' }}
    uses: ./.github/workflows/lambdas.yml

  terraform:
    name: Infrastructure CI
    needs: changes
    if: ${{ needs.changes.outputs.terraform == 'true' }}
    uses: ./.github/workflows/terraform.yml

  # Always run quality checks
  quality:
    name: Code Quality
    uses: ./.github/workflows/quality.yml
