name: CI

on:
  pull_request:
  push:
    branches: [main]

# Avoid duplicate runs on the same ref
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  web:
    name: Web (Next.js)
    runs-on: ubuntu-latest
    if: >-
      contains(github.event_name, 'pull_request') ||
      contains(github.ref, 'refs/heads/')
    # Only run if web or root files changed
    continue-on-error: false
    steps:
      - uses: actions/checkout@v4

      - name: Use Node
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "npm"
          cache-dependency-path: |
            package-lock.json
            **/package-lock.json

      - name: Install deps (root + web)
        run: |
          npm ci --workspaces --include-workspace-root
      
      - name: Lint
        working-directory: apps/web
        run: npm run lint
      
      # TODO: Re-enable typecheck once we fix the errors
      #- name: Typecheck
      #  working-directory: apps/web
      #  run: npm run typecheck

      - name: Build
        working-directory: apps/web
        run: npm run build

  lambdas:
    name: <PERSON><PERSON> (Python)
    runs-on: ubuntu-latest
    # Only run if Python or lambda dirs changed
    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"
          cache: "pip"
          cache-dependency-path: services/lambdas/*/requirements.txt

      - name: Install tooling (ruff, mypy, pytest)
        run: |
          python -m pip install --upgrade pip
          pip install ruff mypy pytest

      - name: Install per-lambda requirements (if any)
        shell: bash
        run: |
          set -e
          shopt -s nullglob
          files=(services/lambdas/*/requirements.txt)
          if [ ${#files[@]} -gt 0 ]; then
            for req in "${files[@]}"; do
              echo "Installing: $req"
              pip install -r "$req"
            done
          else
            echo "No lambda requirements.txt found"
          fi

      - name: Ruff (lint)
        run: |
          ruff check services/lambdas

      - name: MyPy (types)
        # Won't fail the whole build if you haven't added type hints yet
        continue-on-error: true
        run: |
          mypy services/lambdas

      - name: PyTests (if present)
        shell: bash
        run: |
          if ls services/lambdas/**/tests -d 1>/dev/null 2>&1; then
            pytest -q
          else
            echo "No Python tests found"
          fi

  terraform:
    name: Terraform (fmt/validate/tflint)
    runs-on: ubuntu-latest
    env:
      TF_IN_AUTOMATION: true
      TF_INPUT: 0
    steps:
      - uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.9.7

      - name: Terraform fmt (check)
        run: terraform fmt -check -recursive

      - name: Validate envs/dev (no backend)
        working-directory: infra/terraform/envs/dev
        run: |
          terraform init -backend=false -input=false
          terraform validate -no-color

      - name: Install tflint
        run: |
          curl -s https://raw.githubusercontent.com/terraform-linters/tflint/master/install_linux.sh | bash
          echo "$HOME/.tflint/bin" >> $GITHUB_PATH

      - name: tflint (root)
        run: |
          tflint --init
          tflint -f compact

      # Optionally scan each module/env separately
      - name: tflint modules
        shell: bash
        run: |
          set -e
          for dir in infra/terraform/modules/*; do
            [ -d "$dir" ] || continue
            echo "===> tflint $dir"
            (cd "$dir" && tflint -f compact || true)
          done
