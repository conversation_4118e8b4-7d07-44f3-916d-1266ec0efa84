name: Code Quality

on:
  pull_request:
  push:
    branches: [main]

# Avoid duplicate runs on the same ref
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  formatting:
    name: Code Formatting
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "npm"

      - name: Install dependencies
        run: npm ci --include-workspace-root

      - name: Check Prettier formatting
        run: npx prettier --check .

      - name: Check for lint-staged compliance
        run: |
          # This ensures all files pass the lint-staged rules
          npx lint-staged --diff="origin/main...HEAD" || echo "No staged files to check"

  security:
    name: Security Audit
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "npm"

      - name: Install dependencies
        run: npm ci --include-workspace-root

      - name: Run security audit
        run: npm audit --audit-level=moderate

      - name: Check for known vulnerabilities
        run: |
          # Check each workspace for vulnerabilities
          npm audit --workspaces --audit-level=moderate
