name: Web App CI

on:
  pull_request:
    paths:
      - "apps/web/**"
      - "package.json"
      - "package-lock.json"
      - ".github/workflows/web.yml"
  push:
    branches: [main]
    paths:
      - "apps/web/**"
      - "package.json"
      - "package-lock.json"
      - ".github/workflows/web.yml"

# Avoid duplicate runs on the same ref
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  web:
    name: Web App (Next.js)
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "npm"
          cache-dependency-path: |
            package-lock.json
            **/package-lock.json

      - name: Install dependencies
        run: |
          npm ci --workspaces --include-workspace-root

      - name: Lint
        run: npm run lint --workspace apps/web

      - name: Type check
        run: npm run typecheck --workspace apps/web

      - name: Build
        run: npm run build --workspace apps/web

      - name: Test (if tests exist)
        run: |
          if [ -f "package.json" ] && grep -q '"test"' package.json; then
            npm test --workspace apps/web
          else
            echo "No tests found, skipping..."
          fi
