{"compilerOptions": {"target": "ES2022", "lib": ["dom", "dom.iterable", "es2022"], "allowJs": true, "skipLibCheck": true, "strict": true, "noUncheckedIndexedAccess": true, "noEmit": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": "./", "paths": {"@/*": ["src/*"]}, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src"], "exclude": ["node_modules"]}